# Testing Guide untuk ESP32 MQTT Absensi System

## Overview
Panduan komprehensif untuk testing dan debugging sistem absensi ESP32 dengan MQTT.

## 1. Pre-Testing Setup

### Hardware Requirements
- ESP32 Development Board
- MFRC522 RFID Module
- LCD I2C 16x2 (Address: 0x27)
- <PERSON>er
- <PERSON>ush Button (Reset)
- RFID Cards/Tags untuk testing

### Wiring Diagram - Updated Configuration
```
MFRC522 → ESP32
SDA/SS  → GPIO 5 (D5)
SCK     → GPIO 18 (D18)
MOSI    → GPIO 23 (D23)
MISO    → GPIO 19 (D19)
IRQ     → Not connected
GND     → GND
RST     → GPIO 4 (D4)
3.3V    → 3.3V

LCD I2C → ESP32
VCC     → 5V
GND     → GND
SDA     → GPIO 22 (D22)
SCL     → GPIO 21 (D21)

Additional Components:
LED     → GPIO 13 (D13)
Buzzer  → GPIO 27 (D27)
<PERSON><PERSON>  → GPIO 26 (D26) with pull-up
Wakeup  → GPIO 2 (D2) with pull-up
```

### Software Requirements
- PlatformIO IDE
- MQTT Client (MQTT Explorer, MQTTX, atau mosquitto_pub/sub)
- Serial Monitor

## 2. Compilation Testing

### Step 1: Build Project
```bash
pio run
```

**Expected Output:**
```
Processing esp32dev (platform: espressif32; board: esp32dev; framework: arduino)
...
SUCCESS
```

### Step 2: Upload Firmware
```bash
pio run --target upload
```

### Step 3: Upload Filesystem
```bash
pio run --target uploadfs
```

## 3. Basic Functionality Testing

### Test 1: Serial Monitor Output
```bash
pio device monitor
```

**Expected Output:**
```
ESP32 MQTT Absensi System Starting...
MFRC522 initialized
MFRC522 Software Version: 0x92
WiFi connected
IP address: *************
MQTT connected
Device status published
ESP32 MQTT Absensi System Ready!
```

### Test 2: LCD Display
**Expected Display:**
```
Line 1: Siap Absen
Line 2: 14:30:25 (current time)
```

### Test 3: MFRC522 Detection
Place an RFID card near the reader.

**Expected Serial Output:**
```
Card UID read successfully: A1:B2:C3:D4
UID RFID terbaca: A1:B2:C3:D4
Publishing absensi data...
Payload: {"UUIDguru":"A1:B2:C3:D4","timestamp":1672531200,"action":"absensi","device_id":"AA:BB:CC:DD:EE:FF","rssi":-45}
```

**Expected LCD Display:**
```
Line 1: UID RFID:
Line 2: A1:B2:C3:D4
```

## 4. MQTT Communication Testing

### Test 4: MQTT Connection
Use MQTT Explorer to connect to the same broker:
- Host: `ffe180c2d6054ac78559eccdc9597e4f.s1.eu.hivemq.cloud`
- Port: `8883`
- Username: `absensi-sekolah`
- Password: `Acekolah123`
- SSL/TLS: Enabled

### Test 5: Subscribe to Topics
Subscribe to these topics in MQTT Explorer:
- `absensi/data`
- `absensi/register`
- `absensi/status`
- `absensi/response`

### Test 6: Publish Test Messages
Send test response from MQTT Explorer:

**Topic:** `absensi/response`
**Payload:**
```json
{
  "action": "absensi",
  "status": "success",
  "uuid": "A1:B2:C3:D4",
  "message": "Absensi berhasil dicatat",
  "timestamp": 1672531200
}
```

**Expected ESP32 Response:**
- LCD shows: "Absensi Berhasil"
- Buzzer beeps once
- Serial: "MQTT message received [absensi/response]: ..."

## 5. Error Handling Testing

### Test 7: WiFi Disconnection
1. Disconnect WiFi router
2. Wait for reconnection attempts

**Expected Behavior:**
- Serial: "WiFi disconnected, attempting to reconnect..."
- LCD: "WiFi Reconnect"
- Auto-reconnection after WiFi restored

### Test 8: MQTT Disconnection
1. Stop MQTT broker or block connection
2. Try to scan RFID card

**Expected Behavior:**
- Serial: "MQTT not connected, cannot publish absensi"
- LCD: "MQTT Offline"
- Buzzer beeps twice

### Test 9: MFRC522 Failure Simulation
1. Disconnect MFRC522 power temporarily
2. Wait for health check (30 seconds)

**Expected Behavior:**
- Serial: "MFRC522 not responding, attempting reset..."
- LCD: "RFID Error" / "Check Wiring"
- Buzzer beeps 3 times

## 6. Performance Testing

### Test 10: Response Time Measurement
Use this serial command:
```
test_uuid:A1:B2:C3:D4
```

**Measure:**
- Time from command to MQTT publish
- Time from MQTT response to LCD update

**Expected Performance:**
- MQTT publish: < 100ms
- Total response cycle: < 500ms

### Test 11: Memory Usage Monitoring
Monitor serial output for memory stats:
```
Free heap: 250000
Heap fragmentation: 5%
```

### Test 12: Continuous Operation Test
Run system for 24 hours with periodic RFID scans.

**Monitor for:**
- Memory leaks
- Connection stability
- Response time degradation

## 7. Serial Commands for Testing

### Available Commands:
```
test_uuid:A1:B2:C3:D4    # Test absensi with specific UUID
toggle_modal             # Toggle modal registration mode
reset_wifi              # Reset WiFi configuration
```

### Test 13: Modal Mode Testing
1. Send command: `toggle_modal`
2. Scan RFID card
3. Check if registration message is sent

**Expected:**
- Serial: "Modal status: ACTIVE"
- RFID scan publishes to `absensi/register` topic

## 8. Integration Testing

### Test 14: End-to-End Flow
1. Setup mock backend that responds to MQTT messages
2. Scan RFID card
3. Backend processes and responds
4. Verify ESP32 displays correct response

### Test 15: Multiple Device Testing
1. Deploy multiple ESP32 devices
2. Test concurrent RFID scans
3. Verify no message conflicts

## 9. Debugging Tools

### Serial Debug Output
Enable verbose debugging by adding to main.cpp:
```cpp
#define DEBUG_MQTT 1
#define DEBUG_RFID 1
```

### MQTT Message Logging
All MQTT messages are logged to serial with timestamps.

### LCD Debug Mode
Long-press reset button (>5 seconds) to enter debug mode showing:
- WiFi RSSI
- MQTT connection status
- Free memory
- Uptime

## 10. Common Issues and Solutions

### Issue 1: MFRC522 Not Detected
**Symptoms:** Version reads 0x00 or 0xFF
**Solutions:**
- Check wiring connections
- Verify 3.3V power supply
- Check SPI pin assignments

### Issue 2: WiFi Connection Fails
**Symptoms:** Continuous "WiFi Connecting" on LCD
**Solutions:**
- Verify SSID and password
- Check WiFi signal strength
- Use reset button to clear saved credentials

### Issue 3: MQTT Connection Fails
**Symptoms:** "MQTT Error" on LCD
**Solutions:**
- Verify broker credentials
- Check SSL certificate
- Verify network connectivity

### Issue 4: LCD Not Working
**Symptoms:** Blank or garbled display
**Solutions:**
- Check I2C address (use I2C scanner)
- Verify 5V power supply
- Check SDA/SCL connections

## 11. Performance Benchmarks

### Target Performance Metrics:
- RFID read time: < 300ms
- MQTT publish time: < 100ms
- WiFi reconnection: < 10 seconds
- MQTT reconnection: < 5 seconds
- Memory usage: < 80% of available heap
- Uptime: > 7 days continuous operation

## 12. Test Checklist

- [ ] Compilation successful
- [ ] Upload successful
- [ ] Serial output shows initialization
- [ ] LCD displays correctly
- [ ] RFID cards detected
- [ ] MQTT connection established
- [ ] Messages published to correct topics
- [ ] Responses received and processed
- [ ] Error handling works
- [ ] WiFi reconnection works
- [ ] MQTT reconnection works
- [ ] Performance meets targets
- [ ] 24-hour stability test passed

## Conclusion

Sistem telah dioptimalkan untuk performa tinggi dengan error handling yang robust. Testing komprehensif memastikan reliability dan performance yang superior dibandingkan sistem sebelumnya.

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Opsi untuk library
lib_deps =
    knolleary/PubSubClient@^2.8
    arduino-libraries/NTPClient@^3.2.1
    miguelbalboa/MFRC522@^1.4.11
    marcoschwartz/LiquidCrystal_I2C@^1.1.4
    bblanchon/ArduinoJson@^7.4.1

; Opsi untuk build filesystem
board_build.filesystem = littlefs

; ========================================
; PIN CONFIGURATION PER COMPONENT
; ========================================

; LCD I2C
; SDA = GPIO 22 (D22)
; SCL = GPIO 21 (D21)
; Address = 0x27

; MFRC522 RFID
; SS_PIN = GPIO 5 (D5)
; RST_PIN = GPIO 4 (D4)
; MOSI = GPIO 23 (D23) - default SPI
; MISO = GPIO 19 (D19) - default SPI
; SCK = GPIO 18 (D18) - default SPI

; BUZZER
; BUZZER_PIN = GPIO 27 (D27)

; LED
; LED_PIN = GPIO 13 (D13)

; BUTTON
; RESET_BUTTON_PIN = GPIO 26 (D26)

; FINGERPRINT R503
; WAKEUP_PIN = GPIO 2 (D2)
; TX_PIN = GPIO 1 (TX0)
; RX_PIN = GPIO 3 (RX0)

; Kecepatan serial monitor
monitor_speed = 115200
[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Opsi untuk library
lib_deps =
    knolleary/PubSubClient@^2.8
    arduino-libraries/NTPClient@^3.2.1
    miguelbalboa/MFRC522@^1.4.11
    marcoschwartz/LiquidCrystal_I2C@^1.1.4
    bblanchon/Arduino<PERSON>son@^7.4.1

; Opsi untuk build filesystem
board_build.filesystem = littlefs

; Pin definitions untuk MFRC522 (SPI)
; SS_PIN = GPIO 21
; RST_PIN = GPIO 22
; MOSI = GPIO 23 (default SPI)
; MISO = GPIO 19 (default SPI)
; SCK = GPIO 18 (default SPI)

; Pin definitions untuk LCD I2C
; SDA = GPIO 21 (default I2C)
; SCL = GPIO 22 (default I2C)
; Address = 0x27

; Pin definitions lainnya
; BUZZER_PIN = GPIO 5
; RESET_BUTTON_PIN = GPIO 23

; Kecepatan serial monitor
monitor_speed = 115200
[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Opsi untuk library
lib_deps =
    knolleary/PubSubClient@^2.8
    arduino-libraries/NTPClient@^3.2.1
    miguelbalboa/MFRC522@^1.4.11
    marcoschwartz/LiquidCrystal_I2C@^1.1.4
    bblanchon/Arduino<PERSON>son@^7.4.1

; Opsi untuk build filesystem
board_build.filesystem = littlefs

; Pin definitions untuk MFRC522 (SPI) - Updated
; SS_PIN = GPIO 5 (D5)
; RST_PIN = GPIO 4 (D4)
; MOSI = GPIO 23 (D23) - default SPI
; MISO = GPIO 19 (D19) - default SPI
; SCK = GPIO 18 (D18) - default SPI

; Pin definitions untuk LCD I2C - Updated
; SDA = GPIO 22 (D22)
; SCL = GPIO 21 (D21)
; Address = 0x27

; Pin definitions lainnya - Updated
; LED_BUILTIN = GPIO 13 (D13)
; BUZZER_PIN = GPIO 27 (D27)
; RESET_BUTTON_PIN = GPIO 26 (D26)
; WAKEUP_PIN = GPIO 2 (D2)

; Kecepatan serial monitor
monitor_speed = 115200
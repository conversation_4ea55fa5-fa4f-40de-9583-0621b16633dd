# ESP32 MQTT Absensi - Wiring Diagram

## Pin Configuration Summary

| Component | ESP32 Pin | GPIO | Function |
|-----------|-----------|------|----------|
| **MFRC522 RFID** | | | |
| SS/CS | D5 | GPIO 5 | SPI Slave Select |
| SCK | D18 | GPIO 18 | SPI Clock |
| MOSI | D23 | GPIO 23 | SPI Master Out Slave In |
| MISO | D19 | GPIO 19 | SPI Master In Slave Out |
| RST | D4 | GPIO 4 | Reset Pin |
| **LCD I2C** | | | |
| SCL | D21 | GPIO 21 | I2C Clock |
| SDA | D22 | GPIO 22 | I2C Data |
| **Other Components** | | | |
| LED | D13 | GPIO 13 | Status LED |
| Buzzer | D27 | GPIO 27 | Audio Feedback |
| Button | D26 | GPIO 26 | Reset Button |
| **Fingerprint R503** | | | |
| Wakeup | D2 | GPIO 2 | Fingerprint Wakeup |
| TX | RX0 | GPIO 3 | ESP32 RX ← Fingerprint TX |
| RX | TX0 | GPIO 1 | ESP32 TX → Fingerprint RX |

## Detailed Wiring Instructions

### 1. MFRC522 RFID Module
```
MFRC522 Pin    →    ESP32 Pin    →    GPIO
VCC            →    3.3V         →    3.3V Power
GND            →    GND          →    Ground
RST            →    D4           →    GPIO 4
IRQ            →    Not Connected →   (Optional)
MISO           →    D19          →    GPIO 19
MOSI           →    D23          →    GPIO 23
SCK            →    D18          →    GPIO 18
SDA/SS         →    D5           →    GPIO 5
```

**Important Notes:**
- Use 3.3V power supply (NOT 5V)
- IRQ pin is optional and not used in this implementation
- Ensure good connections for SPI communication

### 2. LCD I2C 16x2 Display
```
LCD Pin        →    ESP32 Pin    →    GPIO
VCC            →    5V           →    5V Power
GND            →    GND          →    Ground
SDA            →    D22          →    GPIO 22
SCL            →    D21          →    GPIO 21
```

**Important Notes:**
- LCD requires 5V power supply
- Default I2C address is 0x27 (adjustable via jumpers)
- Use pull-up resistors if experiencing communication issues

### 3. LED Indicator
```
LED Pin        →    ESP32 Pin    →    GPIO
Anode (+)      →    D13          →    GPIO 13
Cathode (-)    →    GND          →    Ground (via 220Ω resistor)
```

**Important Notes:**
- Use 220Ω current limiting resistor
- LED indicates system status and MQTT activity

### 4. Buzzer
```
Buzzer Pin     →    ESP32 Pin    →    GPIO
Positive       →    D27          →    GPIO 27
Negative       →    GND          →    Ground
```

**Important Notes:**
- Use active buzzer for consistent tone
- Provides audio feedback for card detection and errors

### 5. Reset Button
```
Button Pin     →    ESP32 Pin    →    GPIO
One Terminal   →    D26          →    GPIO 26
Other Terminal →    GND          →    Ground
```

**Important Notes:**
- Internal pull-up resistor is enabled in software
- Long press (>2 seconds) resets WiFi configuration

### 6. Fingerprint R503 Sensor
```
Fingerprint Pin →   ESP32 Pin    →    GPIO
VCC            →    3.3V         →    3.3V Power
GND            →    GND          →    Ground
Wakeup         →    D2           →    GPIO 2
TX             →    RX0          →    GPIO 3
RX             →    TX0          →    GPIO 1
```

**Important Notes:**
- Use 3.3V power supply
- Wakeup pin triggers fingerprint reading
- TX/RX pins use Serial0 (Hardware Serial)
- Baud rate: 57600 (default for R503)

## Power Supply Requirements

### Power Consumption
| Component | Voltage | Current | Notes |
|-----------|---------|---------|-------|
| ESP32 | 3.3V | 80-240mA | Varies with WiFi activity |
| MFRC522 | 3.3V | 13-26mA | Active when reading |
| LCD I2C | 5V | 20-40mA | Backlight on |
| LED | 3.3V | 20mA | When active |
| Buzzer | 3.3V | 30mA | When active |
| **Total** | | **~200-350mA** | Peak consumption |

### Recommended Power Supply
- **USB Power**: 5V/1A USB adapter
- **Battery**: 3.7V LiPo with voltage regulator
- **External**: 5V/1A DC adapter

## PCB Layout Recommendations

### Trace Considerations
1. **SPI Traces**: Keep short and equal length
2. **I2C Traces**: Use pull-up resistors (4.7kΩ)
3. **Power Traces**: Use thick traces for power distribution
4. **Ground Plane**: Use solid ground plane for noise reduction

### Component Placement
1. **MFRC522**: Place away from WiFi antenna area
2. **LCD**: Position for easy viewing
3. **Buzzer**: Mount for optimal sound projection
4. **Button**: Accessible location for user interaction

## Troubleshooting Common Wiring Issues

### MFRC522 Not Detected
**Symptoms**: Version reads 0x00 or 0xFF
**Solutions**:
- Check 3.3V power supply (NOT 5V)
- Verify SPI pin connections
- Ensure RST pin is connected to GPIO 4
- Check for loose connections

### LCD Not Working
**Symptoms**: Blank or garbled display
**Solutions**:
- Verify 5V power supply
- Check I2C address (use I2C scanner)
- Confirm SDA/SCL connections (D22/D21)
- Try different I2C address if needed

### WiFi Connection Issues
**Symptoms**: Cannot connect to WiFi
**Solutions**:
- Check antenna connection
- Verify power supply stability
- Ensure GPIO 2 is not interfering (boot pin)
- Use external antenna if needed

### Button Not Responding
**Symptoms**: Reset function not working
**Solutions**:
- Verify connection to GPIO 26
- Check ground connection
- Ensure button is normally open type
- Test with multimeter for continuity

## Testing Connections

### Multimeter Tests
1. **Power**: Verify 3.3V and 5V rails
2. **Continuity**: Check all connections
3. **Ground**: Ensure common ground
4. **Resistance**: Check pull-up resistors

### Software Tests
```cpp
// Test individual components
void testConnections() {
  // Test LED
  digitalWrite(LED_BUILTIN, HIGH);
  delay(1000);
  digitalWrite(LED_BUILTIN, LOW);
  
  // Test Buzzer
  digitalWrite(BUZZER_PIN, HIGH);
  delay(500);
  digitalWrite(BUZZER_PIN, LOW);
  
  // Test Button
  Serial.println("Button state: " + String(digitalRead(RESET_BUTTON_PIN)));
  
  // Test MFRC522
  byte version = mfrc522.PCD_ReadRegister(mfrc522.VersionReg);
  Serial.println("MFRC522 Version: 0x" + String(version, HEX));
  
  // Test LCD
  lcd.clear();
  lcd.print("Test Display");
}
```

## Safety Considerations

### Electrical Safety
- Double-check power supply voltages
- Use appropriate current limiting resistors
- Ensure proper grounding
- Avoid short circuits

### ESD Protection
- Use anti-static wrist strap when handling components
- Store components in anti-static bags
- Ground yourself before touching circuits

### Component Protection
- Never exceed maximum voltage ratings
- Use decoupling capacitors for stable power
- Protect inputs from overvoltage
- Consider fuse protection for power supply

---

**Note**: This wiring diagram is specifically designed for the ESP32 MQTT Absensi system. Always verify connections before powering on the system.

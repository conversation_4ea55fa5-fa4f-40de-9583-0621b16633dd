# Pin Configuration Update - ESP32 MQTT Absensi

## 📌 Summary of Changes

Konfigurasi pin telah diupdate sesuai dengan wiring diagram yang baru untuk mengoptimalkan layout dan kompatibilitas hardware.

## 🔄 Pin Mapping Changes

### Before vs After Comparison

| Component | Function | Old Pin | New Pin | GPIO | Notes |
|-----------|----------|---------|---------|------|-------|
| **MFRC522** | | | | | |
| | SS/CS | D21 | **D5** | GPIO 5 | Moved for better SPI layout |
| | RST | D22 | **D4** | GPIO 4 | Separated from I2C pins |
| | SCK | D18 | D18 | GPIO 18 | Unchanged (default SPI) |
| | MISO | D19 | D19 | GPIO 19 | Unchanged (default SPI) |
| | MOSI | D23 | D23 | GPIO 23 | Unchanged (default SPI) |
| **LCD I2C** | | | | | |
| | SDA | D21 | **D22** | GPIO 22 | Swapped with SCL |
| | SCL | D22 | **D21** | GPIO 21 | Swapped with SDA |
| **Other** | | | | | |
| | LED | D2 | **D13** | GPIO 13 | Moved from boot pin |
| | Buzzer | D5 | **D27** | GPIO 27 | Moved to avoid SPI conflict |
| | Button | D23 | **D26** | GPIO 26 | Moved to avoid SPI conflict |
| | Wakeup | - | **D2** | GPIO 2 | New pin for future features |

## 🎯 Benefits of New Configuration

### 1. **Better Pin Separation**
- **SPI pins** (MFRC522) now properly separated from I2C pins (LCD)
- **Boot-sensitive pins** (GPIO 2) no longer used for critical functions
- **Dedicated pins** for each function type

### 2. **Improved Hardware Layout**
- **Cleaner PCB routing** with logical pin grouping
- **Reduced interference** between different communication protocols
- **Better signal integrity** for high-speed SPI communication

### 3. **Enhanced Reliability**
- **GPIO 2** (boot pin) now only used for optional wakeup function
- **No conflicts** between SPI and I2C communications
- **Proper pin allocation** for each peripheral type

## 🔧 Code Changes Made

### 1. **Pin Definitions Updated**
```cpp
// Old configuration
#define LED_BUILTIN 2
#define SS_PIN 21
#define RST_PIN 22
#define BUZZER_PIN 5
#define RESET_BUTTON_PIN 23

// New configuration
#define LED_BUILTIN 13        // D13 → LED
#define SS_PIN 5              // D5 → RFID (SS/CS)
#define RST_PIN 4             // D4 → RFID (RST)
#define BUZZER_PIN 27         // D27 → Buzzer
#define RESET_BUTTON_PIN 26   // D26 → Tombol
#define WAKEUP_PIN 2          // D2 → Wakeup
```

### 2. **Setup Function Updated**
```cpp
void setup() {
  // Initialize pins dengan konfigurasi baru
  pinMode(LED_BUILTIN, OUTPUT);        // D13 → LED
  pinMode(BUZZER_PIN, OUTPUT);         // D27 → Buzzer
  pinMode(RESET_BUTTON_PIN, INPUT_PULLUP); // D26 → Tombol
  pinMode(WAKEUP_PIN, INPUT_PULLUP);   // D2 → Wakeup (optional)
  // ...
}
```

### 3. **New Wakeup Function Added**
```cpp
void checkWakeupPin() {
  // Function untuk monitoring wakeup pin
  // Bisa digunakan untuk deep sleep functionality di masa depan
  static bool lastWakeupState = HIGH;
  bool currentWakeupState = digitalRead(WAKEUP_PIN);
  
  if (lastWakeupState == HIGH && currentWakeupState == LOW) {
    Serial.println("Wakeup pin activated");
    // Future implementation: wake from deep sleep
  }
  
  lastWakeupState = currentWakeupState;
}
```

## 📋 Updated Wiring Instructions

### MFRC522 RFID Module
```
MFRC522 Pin → ESP32 Pin → GPIO
VCC         → 3.3V      → 3.3V
GND         → GND       → GND
RST         → D4        → GPIO 4
MISO        → D19       → GPIO 19
MOSI        → D23       → GPIO 23
SCK         → D18       → GPIO 18
SDA/SS      → D5        → GPIO 5
```

### LCD I2C Display
```
LCD Pin → ESP32 Pin → GPIO
VCC     → 5V        → 5V
GND     → GND       → GND
SDA     → D22       → GPIO 22
SCL     → D21       → GPIO 21
```

### Additional Components
```
Component → ESP32 Pin → GPIO
LED       → D13       → GPIO 13
Buzzer    → D27       → GPIO 27
Button    → D26       → GPIO 26
Wakeup    → D2        → GPIO 2
```

## ⚠️ Important Notes

### 1. **Power Supply Requirements**
- **MFRC522**: 3.3V only (NOT 5V)
- **LCD**: 5V for backlight and logic
- **LED**: Use 220Ω resistor
- **Total current**: ~200-350mA peak

### 2. **Boot Pin Considerations**
- **GPIO 2** is now used only for optional wakeup
- **No interference** with boot process
- **Safe for deep sleep** wake-up functionality

### 3. **SPI vs I2C Separation**
- **SPI pins** (5, 18, 19, 23, 4) dedicated to MFRC522
- **I2C pins** (21, 22) dedicated to LCD
- **No shared pins** between protocols

## 🧪 Testing Checklist

After implementing pin changes, verify:

- [ ] **MFRC522 Detection**: Check version register reads correctly
- [ ] **LCD Display**: Verify text appears correctly
- [ ] **LED Function**: Test on/off control
- [ ] **Buzzer Sound**: Verify audio feedback
- [ ] **Button Response**: Test reset functionality
- [ ] **Wakeup Pin**: Monitor for future functionality
- [ ] **SPI Communication**: Verify RFID card reading
- [ ] **I2C Communication**: Verify LCD updates
- [ ] **No Pin Conflicts**: All functions work simultaneously

## 🔮 Future Enhancements

### Deep Sleep Mode
With GPIO 2 now available as wakeup pin:
```cpp
// Future implementation
esp_sleep_enable_ext0_wakeup(GPIO_NUM_2, 0); // Wake on LOW
esp_deep_sleep_start();
```

### Power Management
- **Sleep between card reads** for battery operation
- **Wake on button press** or card detection
- **Reduced power consumption** for portable applications

## 📁 Files Updated

1. **`src/main.cpp`** - Pin definitions and setup code
2. **`platformio.ini`** - Pin documentation in comments
3. **`TESTING_GUIDE.md`** - Updated wiring diagram
4. **`PROJECT_SUMMARY.md`** - Updated pin configuration
5. **`WIRING_DIAGRAM.md`** - Detailed wiring instructions
6. **`PIN_CONFIGURATION_UPDATE.md`** - This summary document

## ✅ Verification

All changes have been implemented and tested for:
- ✅ **Compilation success** with new pin definitions
- ✅ **No GPIO conflicts** between different peripherals
- ✅ **Proper pin initialization** in setup function
- ✅ **Updated documentation** across all files
- ✅ **Future-ready** for additional features

---

**Status**: ✅ **PIN CONFIGURATION SUCCESSFULLY UPDATED**

Sistem sekarang menggunakan konfigurasi pin yang optimal untuk performa dan reliability yang lebih baik.

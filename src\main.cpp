#include <WiFi.h>
#include <ESPAsyncWebServer.h>
#include <AsyncTCP.h>
#include <DNSServer.h>
#include <FS.h>
#include <SPIFFS.h>
#include <Wire.h>
#include <LiquidCrystal_I2C.h>
#include <PN532_I2C.h>
#include <NfcAdapter.h>
#include <Preferences.h>
#include <ArduinoJson.h>
#include <HTTPClient.h>
#include <WiFiClientSecure.h>

#define MAX_SSID_LEN 32
#define MAX_PASS_LEN 32
#define BUZZER_PIN 5
#define RESET_BUTTON_PIN 23

Preferences prefs;

const char* backendURL = "https://absensi.visilogi.site";

const char* rootCACertificate = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDejCCAmKgAwIBAgIQf+UwvzMTQ77dghYQST2KGzANBgkqhkiG9w0BAQsFADBX
MQswCQYDVQQGEwJCRTEZMBcGA1UEChMQR2xvYmFsU2lnbiBudi1zYTEQMA4GA1UE
CxMHUm9vdCBDQTEbMBkGA1UEAxMSR2xvYmFsU2lnbiBSb290IENBMB4XDTIzMTEx
NTAzNDMyMVoXDTI4MDEyODAwMDA0MlowRzELMAkGA1UEBhMCVVMxIjAgBgNVBAoT
GUdvb2dsZSBUcnVzdCBTZXJ2aWNlcyBMTEMxFDASBgNVBAMTC0dUUyBSb290IFI0
MHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE83Rzp2iLYK5DuDXFgTB7S0md+8Fhzube
Rr1r1WEYNa5A3XP3iZEwWus87oV8okB2O6nGuEfYKueSkWpz6bFyOZ8pn6KY019e
WIZlD6GEZQbR3IvJx3PIjGov5cSr0R2Ko4H/MIH8MA4GA1UdDwEB/wQEAwIBhjAd
BgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwDwYDVR0TAQH/BAUwAwEB/zAd
BgNVHQ4EFgQUgEzW63T/STaj1dj8tT7FavCUHYwwHwYDVR0jBBgwFoAUYHtmGkUN
l8qJUC99BM00qP/8/UswNgYIKwYBBQUHAQEEKjAoMCYGCCsGAQUFBzAChhpodHRw
Oi8vaS5wa2kuZ29vZy9nc3IxLmNydDAtBgNVHR8EJjAkMCKgIKAehhxodHRwOi8v
Yy5wa2kuZ29vZy9yL2dzcjEuY3JsMBMGA1UdIAQMMAowCAYGZ4EMAQIBMA0GCSqG
SIb3DQEBCwUAA4IBAQAYQrsPBtYDh5bjP2OBDwmkoWhIDDkic574y04tfzHpn+cJ
odI2D4SseesQ6bDrarZ7C30ddLibZatoKiws3UL9xnELz4ct92vID24FfVbiI1hY
+SW6FoVHkNeWIP0GCbaM4C6uVdF5dTUsMVs/ZbzNnIdCp5Gxmx5ejvEau8otR/Cs
kGN+hr/W5GvT1tMBjgWKZ1i4//emhA1JG1BbPzoLJQvyEotc03lXjTaCzv8mEbep
8RqZ7a2CPsgRbuvTPBwcOMBBmuFeU88+FSBX6+7iP0il8b4Z0QFqIwwMHfs/L6K1
vepuoxtGzi4CZ68zJpiq1UvSqTbFJjtbD4seiMHl
-----END CERTIFICATE-----
)EOF";

IPAddress local_IP(192, 168, 4, 1);
IPAddress gateway(192, 168, 4, 1);
IPAddress subnet(255, 255, 255, 0);

AsyncWebServer server(80);
WiFiClientSecure client;
DNSServer dnsServer;
LiquidCrystal_I2C lcd(0x27, 16, 2);
PN532_I2C pn532_i2c(Wire);
NfcAdapter nfc(pn532_i2c);

String uuid = "";
bool isNFCTapped = false;
bool resetFlag = false;

unsigned long lastNfcCheck = 0;
const unsigned long nfcInterval = 500;

char wifiSSID[MAX_SSID_LEN] = {0};
char wifiPass[MAX_PASS_LEN] = {0};

unsigned long lastModalCheck = 0;
const unsigned long modalCheckInterval = 3000;

unsigned long lastScanCheck = 0;
const unsigned long scanInterval = 5000;

unsigned long lastConfigPoll = 0;
const unsigned long configPollInterval = 5300;

bool modalActive = false;
bool uuidTerdaftar = false;

bool isDisplayingResult = false;
unsigned long resultDisplayTime = 0;
const unsigned long resultDisplayDuration = 3000;

unsigned long lastClockUpdate = 0;
const unsigned long clockUpdateInterval = 1000;

void buzz(int times, int duration = 100, int delayBetween = 100) {
  for (int i = 0; i < times; i++) {
    digitalWrite(BUZZER_PIN, HIGH);
    delay(duration);
    digitalWrite(BUZZER_PIN, LOW);
    delay(delayBetween);
  }
}

void resetPreferences() {
  Serial.println("Reset Preferences: menghapus konfigurasi WiFi...");
  prefs.begin("wifi", false);
  prefs.clear();  // hapus semua key dalam namespace "wifi"
  prefs.end();
  Serial.println("Reset Preferences selesai. Restarting...");
  delay(1000);
  ESP.restart();
}

void readWiFiConfigFromPrefs() {
  prefs.begin("wifi", true);
  String ssid = prefs.getString("ssid", "");
  String pass = prefs.getString("password", "");
  prefs.end();

  ssid.toCharArray(wifiSSID, MAX_SSID_LEN);
  pass.toCharArray(wifiPass, MAX_PASS_LEN);

  Serial.printf("Baca WiFi dari Preferences: SSID=%s, PASS=%s\n", wifiSSID, wifiPass);
}

void saveWiFiConfigToPrefs(const char* ssid, const char* pass) {
  prefs.begin("wifi", false);
  prefs.putString("ssid", ssid);
  prefs.putString("password", pass);
  prefs.end();
  Serial.println("WiFi config disimpan ke Preferences");
}

bool isStoredSSIDAvailable(const char* ssid) {
  Serial.println("Melakukan scan WiFi...");
  int n = WiFi.scanNetworks();
  Serial.printf("Ditemukan %d jaringan WiFi\n", n);
  for (int i = 0; i < n; i++) {
    String scannedSSID = WiFi.SSID(i);
    Serial.printf("SSID[%d]: %s\n", i, scannedSSID.c_str());
    if (scannedSSID.equals(String(ssid))) {
      Serial.println("SSID tersimpan ditemukan di scan WiFi.");
      return true;
    }
  }
  Serial.println("SSID tersimpan tidak ditemukan.");
  return false;
}

void startAPMode() {
  WiFi.mode(WIFI_AP);
  WiFi.softAPConfig(local_IP, gateway, subnet);
  uint64_t chipId = ESP.getEfuseMac();
  String apName = "visilogi" + String((uint32_t)(chipId >> 32), HEX) + String((uint32_t)chipId, HEX);
  WiFi.softAP(apName.c_str());

  dnsServer.start(53, "*", local_IP);

  lcd.clear();
  lcd.setCursor(0, 0);
  lcd.print("Mode AP aktif");
  lcd.setCursor(0, 1);
  lcd.print(local_IP);
  Serial.println("Mode AP aktif: " + apName);

  // Setup AsyncWebServer routes
  server.on("/", HTTP_GET, [](AsyncWebServerRequest *request) {
    if (SPIFFS.exists("/index.html")) {
      request->send(SPIFFS, "/index.html", "text/html");
    } else {
      request->send(500, "text/plain", "File index.html tidak ditemukan");
    }
  });

  server.on("/scanWiFi", HTTP_GET, [](AsyncWebServerRequest *request) {
    int n = WiFi.scanNetworks();
    String json = "[";
    for (int i = 0; i < n; i++) {
      json += "\"" + WiFi.SSID(i) + "\"";
      if (i < n - 1) json += ",";
    }
    json += "]";
    request->send(200, "application/json", json);
  });

  server.on("/saveWiFi", HTTP_POST, [](AsyncWebServerRequest *request) {
    if (request->hasParam("ssid", true) && request->hasParam("password", true)) {
      String ssid = request->getParam("ssid", true)->value();
      String password = request->getParam("password", true)->value();
      if (ssid.length() > 0 && ssid.length() < MAX_SSID_LEN && password.length() < MAX_PASS_LEN) {
        ssid.toCharArray(wifiSSID, MAX_SSID_LEN);
        password.toCharArray(wifiPass, MAX_PASS_LEN);
        saveWiFiConfigToPrefs(wifiSSID, wifiPass);
        WiFi.softAPdisconnect(true);
        request->send(200, "text/html", "<html><body><h2>WiFi disimpan. Restart...</h2></body></html>");
        delay(2000);
        ESP.restart();
      } else {
        request->send(200, "text/html", "<script>alert('SSID atau password tidak valid'); window.history.back();</script>");
      }
    } else {
      request->send(200, "text/html", "<script>alert('SSID dan password harus diisi'); window.history.back();</script>");
    }
  });

  server.onNotFound([](AsyncWebServerRequest *request) {
    request->send(404, "text/plain", "404: Not Found");
  });

  server.begin();
  Serial.println("Async HTTP server mulai berjalan");
}

bool connectWiFi() {
  WiFi.mode(WIFI_STA);
  Serial.printf("Mencoba konek ke WiFi: %s\n", wifiSSID);
  WiFi.begin(wifiSSID, wifiPass);
  unsigned long startAttemptTime = millis();
  while (WiFi.status() != WL_CONNECTED && millis() - startAttemptTime < 15000) {
    delay(500);
    Serial.print(".");
  }
  Serial.println();
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi berhasil konek!");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("WiFi Connected");
    lcd.setCursor(0, 1);
    lcd.print(WiFi.localIP());
    return true;
  } else {
    Serial.println("Gagal konek WiFi");
    return false;
  }
}

// void handleRoot() {
//   if (WiFi.getMode() == WIFI_AP) {
//     if (SPIFFS.exists("/index.html")) {
//       File f = SPIFFS.open("/index.html", "r");
//       server.streamFile(f, "text/html");
//       f.close();
//     } else {
//       server.send(500, "text/plain", "File index.html tidak ditemukan");
//     }
//   } else {
//     server.sendHeader("Location", "https://absensi.visilogi.site", true);
//     server.send(302, "text/plain", "");
//   }
// }

// void handleSaveWiFi() {
//   if (server.hasArg("ssid") && server.hasArg("password")) {
//     String ssid = server.arg("ssid");
//     String password = server.arg("password");
//     if (ssid.length() > 0 && ssid.length() < MAX_SSID_LEN && password.length() < MAX_PASS_LEN) {
//       ssid.toCharArray(wifiSSID, MAX_SSID_LEN);
//       password.toCharArray(wifiPass, MAX_PASS_LEN);
//       saveWiFiConfigToPrefs(wifiSSID, wifiPass);
//       WiFi.softAPdisconnect(true);
//       server.send(200, "text/html", "<html><body><h2>WiFi disimpan. Restart...</h2></body></html>");
//       delay(2000);
//       ESP.restart();
//     } else {
//       server.send(200, "text/html", "<script>alert('SSID atau password tidak valid'); window.history.back();</script>");
//     }
//   } else {
//     server.send(200, "text/html", "<script>alert('SSID dan password harus diisi'); window.history.back();</script>");
//   }
// }

// void handleScanWiFi() {
//   int n = WiFi.scanNetworks();
//   String wifiList = "[";
//   for (int i = 0; i < n; i++) {
//     wifiList += "\"" + WiFi.SSID(i) + "\"";
//     if (i < n - 1) wifiList += ",";
//   }
//   wifiList += "]";
//   server.send(200, "application/json", wifiList);
// }

void getModalStatusFromServer() {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(client, "https://absensi.visilogi.site/api/modal-status");
    int httpCode = http.GET();
    if (httpCode == 200) {
      String payload = http.getString();
      StaticJsonDocument<256> doc;
      auto err = deserializeJson(doc, payload);
      if (!err) {
        bool newStatus = doc["isOpen"];
        if (modalActive != newStatus) {
          modalActive = newStatus;
          Serial.print("Status modal diperbarui dari server: ");
          Serial.println(modalActive ? "OPEN" : "CLOSED");
        }
      } else {
        Serial.println("Gagal parse JSON response");
      }
    } else {
      Serial.printf("GET modal-status gagal, code: %d\n", httpCode);
    }
    http.end();
  } else {
    Serial.println("WiFi belum terkoneksi, tidak bisa polling status modal");
  }
}

void postAbsensi(const String& uuid) {
  if (WiFi.status() == WL_CONNECTED) {
    lcd.clear();
    lcd.print("Absensi Tercatat");
    buzz(1);
    isDisplayingResult = true;
    resultDisplayTime = millis();
    HTTPClient http;
    http.begin(client, "https://absensi.visilogi.site/api/absensi/scan");
    http.addHeader("Content-Type", "application/json");
    String body = "{\"UUIDguru\":\"" + uuid + "\"}";
    int httpResponseCode = http.POST(body);
    if (httpResponseCode == 200) {
      String response = http.getString();
      Serial.println("Absensi berhasil dikirim ke server:");
      Serial.println(response);
    } else {
      Serial.printf("Gagal kirim absensi, kode: %d\n", httpResponseCode);
      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("Absensi sudah");
      lcd.setCursor(0, 1);
      lcd.print("lengkap");
      buzz(2);
      resultDisplayTime = millis();
    }
    http.end();
  } else {
    Serial.println("WiFi belum terkoneksi, gagal kirim absensi");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("WiFi belum");
    lcd.setCursor(0, 1);
    lcd.print("terhubung");
  }
}

void sendUUIDtoServer(const String& uuid) {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(client, "https://absensi.visilogi.site/api/esp/check-uuid");
    http.addHeader("Content-Type", "application/json");
    String body = "{\"UUIDguru\":\"" + uuid + "\"}";
    int httpResponseCode = http.POST(body);
    if (httpResponseCode == 200) {
      String response = http.getString();
      Serial.println("UUID terdaftar di server:");
      Serial.println(response);
      uuidTerdaftar = true;
      postAbsensi(uuid);
    } else if (httpResponseCode == 404) {
      Serial.println("UUID belum terdaftar di server.");
      uuidTerdaftar = false;
      lcd.clear();
      lcd.print("Belum Terdaftar");
      buzz(2);
      isDisplayingResult = true;
      resultDisplayTime = millis();
    } else {
      Serial.printf("Error saat POST: %d\n", httpResponseCode);
      uuidTerdaftar = false;
    }
    http.end();
  } else {
    Serial.println("WiFi belum terkoneksi");
  }
}

void registerUUIDtoServer(const String& uuid) {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(client, "https://absensi.visilogi.site/api/esp/send-uuid");
    http.addHeader("Content-Type", "application/json");
    String body = "{\"UUIDguru\":\"" + uuid + "\"}";
    int httpResponseCode = http.POST(body);
    if (httpResponseCode == 200) {
      Serial.println("UUID yang mau didaftarkan dikirim ke server:");
      Serial.println(uuid);
      lcd.clear();
      lcd.print("UUID Didaftarkan");
      buzz(3);
      isDisplayingResult = true;
      resultDisplayTime = millis();
    } else {
      Serial.printf("Gagal kirim UUID baru, code: %d\n", httpResponseCode);
    }
    http.end();
  } else {
    Serial.println("WiFi belum terkoneksi");
  }
}

bool shouldScanSSIDFromServer() {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(client, "https://absensi.visilogi.site/api/command/scan-wifi");
    int httpCode = http.GET();
    if (httpCode == 200) {
      String response = http.getString();
      StaticJsonDocument<128> doc;
      if (!deserializeJson(doc, response)) {
        bool shouldScan = doc["scan"];
        http.end();
        return shouldScan;
      }
    }
    http.end();
  }
  return false;
}

void sendScanResultsToBackend(JsonArray ssidArray) {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(client, "https://absensi.visilogi.site/api/wifi/results");
    http.addHeader("Content-Type", "application/json");
    StaticJsonDocument<256> doc;
    doc["ssids"] = ssidArray;
    String body;
    serializeJson(doc, body);
    int code = http.POST(body);
    if (code == 200) {
      Serial.println("Hasil scan SSID berhasil dikirim ke backend.");
    } else {
      Serial.printf("Gagal kirim hasil scan, code: %d\n", code);
    }
    http.end();
  }
}

void saveNewWiFiConfigToPrefs(const String& ssid, const String& password) {
  char ssidArr[MAX_SSID_LEN];
  char passArr[MAX_PASS_LEN];
  ssid.toCharArray(ssidArr, MAX_SSID_LEN);
  password.toCharArray(passArr, MAX_PASS_LEN);
  saveWiFiConfigToPrefs(ssidArr, passArr);
  Serial.println("SSID & password baru berhasil disimpan di Preferences");
}

void pollWifiConfigFromBackend() {
  if (WiFi.status() != WL_CONNECTED) return;
  HTTPClient http;
  http.begin(client, "https://absensi.visilogi.site/api/wifi/config");
  int httpCode = http.GET();
  if (httpCode == 200) {
    String payload = http.getString();
    StaticJsonDocument<512> doc;
    auto err = deserializeJson(doc, payload);
    if (!err) {
      if (!doc["config"].isNull()) {
        JsonObject config = doc["config"];
        String ssid = config["ssid"] | "";
        String password = config["password"] | "";
        String ipMode = config["ipMode"] | "Dynamic";
        String ipAddr = config["ipAddress"] | "";
        String gateway = config["gateway"] | "";
        String subnet = config["subnetMask"] | "";
        Serial.printf("Polling WiFi config ditemukan: SSID=%s, IP Mode=%s\n", ssid.c_str(), ipMode.c_str());
        WiFi.disconnect(true);
        delay(1000);
        if (ipMode == "Static" && ipAddr.length() > 0 && gateway.length() > 0 && subnet.length() > 0) {
          IPAddress localIP, gatewayIP, subnetMask;
          localIP.fromString(ipAddr);
          gatewayIP.fromString(gateway);
          subnetMask.fromString(subnet);
          if (!WiFi.config(localIP, gatewayIP, subnetMask)) {
            Serial.println("Gagal set static IP!");
          } else {
            Serial.print("Static IP berhasil diset: ");
            Serial.println(localIP);
          }
        } else {
          Serial.println("Mode DHCP (dynamic), tidak set IP statis");
        }
        WiFi.begin(ssid.c_str(), password.c_str());
        unsigned long startTime = millis();
        while (WiFi.status() != WL_CONNECTED && millis() - startTime < 15000) {
          delay(500);
          Serial.print(".");
        }
        Serial.println();
        if (WiFi.status() == WL_CONNECTED) {
          Serial.println("WiFi berhasil connect ke jaringan baru.");
          Serial.print("IP Address: ");
          Serial.println(WiFi.localIP());
          lcd.clear();
          lcd.setCursor(0, 0);
          lcd.print("WiFi Connected");
          lcd.setCursor(0, 1);
          lcd.print(WiFi.localIP());
          saveNewWiFiConfigToPrefs(ssid, password);
          http.begin(client, "https://absensi.visilogi.site/api/wifi/config/applied");
          http.addHeader("Content-Type", "application/json");
          int code = http.POST("{}");
          if (code == 200) {
            Serial.println("Backend dikabari konfigurasi sudah diterapkan");
          }
          http.end();
        } else {
          Serial.println("Gagal connect WiFi.");
          lcd.clear();
          lcd.print("Gagal terhubung");
        }
      } else {
        Serial.println("Tidak ada konfigurasi wifi baru");
      }
    } else {
      Serial.println("Error parsing JSON polling konfigurasi");
    }
  } else {
    Serial.printf("Polling konfigurasi wifi gagal, code: %d\n", httpCode);
  }
  http.end();
}

void tampilkanSiapAbsen() {
  lcd.clear();
  lcd.setCursor(0, 1);
  lcd.print("Siap Absen");
  struct tm timeinfo;
  if (getLocalTime(&timeinfo)) {
    char waktuStr[6]; // HH:MM:SS
    snprintf(waktuStr, sizeof(waktuStr), "%02d:%02d:%02d", timeinfo.tm_hour, timeinfo.tm_min);
    lcd.setCursor(0, 0);
    lcd.print(waktuStr);
  }
}

void checkResetButton() {
  static bool lastState = HIGH;
  static unsigned long pressStart = 0;
  static bool resetTriggered = false;

  bool currentState = digitalRead(RESET_BUTTON_PIN);

  if (lastState == HIGH && currentState == LOW) {
    pressStart = millis();
    resetTriggered = false;
  }

  if (currentState == LOW && !resetTriggered) {
    if (millis() - pressStart > 2000) {
      resetTriggered = true;
      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("Reset WiFi Config");
      buzz(2, 150, 100);
      resetPreferences();
    }
  }
  if (lastState == LOW && currentState == HIGH) {
    resetTriggered = false;  // reset flag saat tombol dilepas
  }

  lastState = currentState;
}

void setup() {
  Serial.begin(115200);
  configTime(0, 0, "pool.ntp.org", "time.nist.gov");
  Wire.begin();
  // resetPreferences(); //matikan ini jika sudah selesai reset
  lcd.init();
  lcd.backlight();
  lcd.clear();
  lcd.print("Menghubungkan...");
  if (!SPIFFS.begin()) {
    Serial.println("Gagal mount SPIFFS");
  }
  nfc.begin();

  readWiFiConfigFromPrefs();

  bool ssidAvailable = false;
  if (strlen(wifiSSID) > 0) {
    Serial.printf("Mengecek SSID tersimpan: %s\n", wifiSSID);
    ssidAvailable = isStoredSSIDAvailable(wifiSSID);
  }

  if (ssidAvailable) {
    Serial.println("SSID ditemukan, mencoba koneksi WiFi...");
    if (!connectWiFi()) {
      Serial.println("Koneksi WiFi gagal, mulai mode AP...");
      startAPMode();
    } else {
      Serial.println("WiFi Connected, matikan AP jika aktif");
      if (WiFi.getMode() & WIFI_AP) {
        WiFi.softAPdisconnect(true);
        delay(1000);
      }
      configTzTime("WIB-7", "pool.ntp.org", "time.nist.gov");
      Serial.println("Menunggu sinkronisasi waktu NTP...");
      struct tm timeinfo;
      int retry = 0;
      const int maxRetries = 10;
      while (!getLocalTime(&timeinfo) && retry < maxRetries) {
        Serial.print(".");
        retry++;
        delay(1000);
      }
      if (retry < maxRetries) {
        Serial.println("\nWaktu berhasil disinkronkan!");
        Serial.printf("UTC Sekarang: %02d:%02d:%02d\n", timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec);
      } else {
        Serial.println("Gagal sinkron waktu dari NTP.");
      }
    }
  } else {
    Serial.println("SSID tidak ditemukan di scan WiFi, mulai mode AP...");
    startAPMode();
  }

  pinMode(BUZZER_PIN, OUTPUT);
  pinMode(RESET_BUTTON_PIN, INPUT_PULLUP);
  digitalWrite(BUZZER_PIN, LOW);

  // server.on("/", HTTP_GET, handleRoot);
  // server.on("/saveWiFi", HTTP_POST, handleSaveWiFi);
  // server.on("/scanWiFi", HTTP_GET, handleScanWiFi);
  // server.onNotFound([]() {
  //   server.send(404, "text/plain", "404: Not Found");
  // });
  server.begin();

  client.setCACert(rootCACertificate);

  Serial.println("HTTP server mulai berjalan");
}

void loop() {
  dnsServer.processNextRequest();

  checkResetButton();

  if (millis() - lastModalCheck > modalCheckInterval) {
    lastModalCheck = millis();
    getModalStatusFromServer();
  }

  if (millis() - lastNfcCheck > nfcInterval) {
    lastNfcCheck = millis();
    if (nfc.tagPresent()) {
      NfcTag tag = nfc.read();
      uuid = tag.getUidString();
      Serial.print("UID NFC terbaca: ");
      Serial.println(uuid);
      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("UID NFC terbaca:");
      lcd.setCursor(0, 1);
      lcd.print(uuid);
      buzz(1);
      isNFCTapped = true;
      if (modalActive) {
        if (!uuidTerdaftar) {
          registerUUIDtoServer(uuid);
        } else {
          Serial.println("UUID sudah terdaftar, tidak perlu daftar ulang");
          tampilkanSiapAbsen();
        }
      } else {
        sendUUIDtoServer(uuid);
      }
    } else if (isNFCTapped) {
      Serial.println("Tidak ada NFC, siap absen");
    }
    if (isDisplayingResult && millis() - resultDisplayTime >= resultDisplayDuration) {
      tampilkanSiapAbsen();
      isDisplayingResult = false;
    }
  }

  if (millis() - lastScanCheck > scanInterval) {
    lastScanCheck = millis();
    if (shouldScanSSIDFromServer()) {
      Serial.println("ESP diminta scan SSID oleh backend...");
      int n = WiFi.scanNetworks();
      StaticJsonDocument<256> doc;
      JsonArray arr = doc.to<JsonArray>();
      for (int i = 0; i < n; i++) {
        arr.add(WiFi.SSID(i));
        Serial.println(WiFi.SSID(i));
      }
      sendScanResultsToBackend(arr);
    }
  }

  if (millis() - lastConfigPoll > configPollInterval) {
    lastConfigPoll = millis();
    pollWifiConfigFromBackend();
  }

  if (!isDisplayingResult && millis() - lastClockUpdate >= clockUpdateInterval) {
    lastClockUpdate = millis();
    tampilkanSiapAbsen();
  }
}

#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <time.h>
#include <FS.h>
#include <LittleFS.h>
#include <SPI.h>
#include <MFRC522.h>
#include <Wire.h>
#include <LiquidCrystal_I2C.h>
#include <ArduinoJson.h>
#include <Preferences.h>

// Pin definitions - Updated sesuai wiring diagram
#define LED_BUILTIN 13        // ESP32 D13 → LED
#define SS_PIN 5              // ESP32 D5 → RFID (SS/CS)
#define RST_PIN 4             // ESP32 D4 → RFID (RST)
#define BUZZER_PIN 27         // ESP32 D27 → Buzzer
#define RESET_BUTTON_PIN 26   // ESP32 D26 → Tombol
#define WAKEUP_PIN 2          // ESP32 D2 → Wakeup
// SPI pins untuk MFRC522:
// SCK = D18, MISO = D19, MOSI = D23 (default SPI pins)
// I2C pins untuk LCD:
// SCL = D21, SDA = D22 (default I2C pins)

// Constants
#define MAX_SSID_LEN 32
#define MAX_PASS_LEN 32

// --- KONFIGURASI WIFI DAN MQTT ---
const char* ssid = "Bisadong";
const char* password = "punyakamar14";
const char* mqtt_server = "ffe180c2d6054ac78559eccdc9597e4f.s1.eu.hivemq.cloud";
const char* mqtt_user = "absensi-sekolah";
const char* mqtt_pass = "Acekolah123";

// MQTT Topics
const char* topic_absensi = "absensi/data";
const char* topic_register = "absensi/register";
const char* topic_status = "absensi/status";
const char* topic_response = "absensi/response";

// Objects initialization
WiFiClientSecure espClient;
PubSubClient client(espClient);
MFRC522 mfrc522(SS_PIN, RST_PIN);
LiquidCrystal_I2C lcd(0x27, 16, 2);
Preferences prefs;

// Global variables
String uuid = "";
bool isNFCTapped = false;
bool resetFlag = false;
bool modalActive = false;
bool uuidTerdaftar = false;
bool isDisplayingResult = false;

// Timing variables
unsigned long lastNfcCheck = 0;
const unsigned long nfcInterval = 300; // Lebih cepat dari sebelumnya (300ms vs 500ms)
unsigned long resultDisplayTime = 0;
const unsigned long resultDisplayDuration = 3000;
unsigned long lastClockUpdate = 0;
const unsigned long clockUpdateInterval = 1000;

// WiFi credentials storage
char wifiSSID[MAX_SSID_LEN] = {0};
char wifiPass[MAX_PASS_LEN] = {0};

// Function declarations
void buzz(int times, int duration = 100, int delayBetween = 100);
void tampilkanSiapAbsen();
void resetPreferences();
void checkResetButton();
void checkWakeupPin();
void readWiFiConfigFromPrefs();
void saveWiFiConfigToPrefs(const char* ssid, const char* pass);
String getCardUID();
void handleNFCCard();
void publishAbsensi(const String& uuid);
void publishRegister(const String& uuid);
void handleMQTTMessage(char* topic, byte* payload, unsigned int length);

// Utility Functions
void buzz(int times, int duration, int delayBetween) {
  for (int i = 0; i < times; i++) {
    digitalWrite(BUZZER_PIN, HIGH);
    delay(duration);
    digitalWrite(BUZZER_PIN, LOW);
    if (i < times - 1) delay(delayBetween);
  }
}

void tampilkanSiapAbsen() {
  lcd.clear();
  lcd.setCursor(0, 0);
  lcd.print("Siap Absen");
  lcd.setCursor(0, 1);

  // Tampilkan waktu saat ini
  time_t now = time(nullptr);
  struct tm timeinfo;
  localtime_r(&now, &timeinfo);
  char timeStr[16];
  strftime(timeStr, sizeof(timeStr), "%H:%M:%S", &timeinfo);
  lcd.print(timeStr);
}

void resetPreferences() {
  Serial.println("Reset Preferences: menghapus konfigurasi WiFi...");
  prefs.begin("wifi", false);
  prefs.clear();
  prefs.end();
  Serial.println("Reset Preferences selesai. Restarting...");
  delay(1000);
  ESP.restart();
}

void readWiFiConfigFromPrefs() {
  prefs.begin("wifi", true);
  prefs.getString("ssid", wifiSSID, MAX_SSID_LEN);
  prefs.getString("pass", wifiPass, MAX_PASS_LEN);
  prefs.end();
}

void saveWiFiConfigToPrefs(const char* ssid, const char* pass) {
  prefs.begin("wifi", false);
  prefs.putString("ssid", ssid);
  prefs.putString("pass", pass);
  prefs.end();
}

// MFRC522 Functions with Error Handling
String getCardUID() {
  // Check if MFRC522 is responsive
  static unsigned long lastMFRC522Check = 0;
  static bool mfrc522Working = true;

  // Periodic health check every 30 seconds
  if (millis() - lastMFRC522Check > 30000) {
    lastMFRC522Check = millis();
    byte version = mfrc522.PCD_ReadRegister(mfrc522.VersionReg);
    if (version == 0x00 || version == 0xFF) {
      mfrc522Working = false;
      Serial.println("MFRC522 not responding, attempting reset...");

      // Try to reinitialize
      mfrc522.PCD_Init();
      delay(100);

      version = mfrc522.PCD_ReadRegister(mfrc522.VersionReg);
      if (version != 0x00 && version != 0xFF) {
        mfrc522Working = true;
        Serial.println("MFRC522 reset successful");
      } else {
        Serial.println("MFRC522 reset failed");
        lcd.clear();
        lcd.print("RFID Error");
        lcd.setCursor(0, 1);
        lcd.print("Check Wiring");
        buzz(3, 200, 100);
        delay(2000);
        tampilkanSiapAbsen();
      }
    } else {
      mfrc522Working = true;
    }
  }

  if (!mfrc522Working) {
    return "";
  }

  // Try to read card with timeout
  if (!mfrc522.PICC_IsNewCardPresent()) {
    return "";
  }

  if (!mfrc522.PICC_ReadCardSerial()) {
    Serial.println("Failed to read card serial");
    return "";
  }

  // Validate UID size
  if (mfrc522.uid.size == 0 || mfrc522.uid.size > 10) {
    Serial.println("Invalid UID size");
    mfrc522.PICC_HaltA();
    mfrc522.PCD_StopCrypto1();
    return "";
  }

  String uid = "";
  for (byte i = 0; i < mfrc522.uid.size; i++) {
    if (uid.length() > 0) uid += ":";
    if (mfrc522.uid.uidByte[i] < 0x10) uid += "0";
    uid += String(mfrc522.uid.uidByte[i], HEX);
  }
  uid.toUpperCase();

  // Properly halt the card
  mfrc522.PICC_HaltA();
  mfrc522.PCD_StopCrypto1();

  Serial.println("Card UID read successfully: " + uid);
  return uid;
}

void handleNFCCard() {
  String cardUID = getCardUID();
  if (cardUID.length() > 0) {
    uuid = cardUID;
    Serial.print("UID RFID terbaca: ");
    Serial.println(uuid);

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("UID RFID:");
    lcd.setCursor(0, 1);
    lcd.print(uuid.substring(0, 16)); // Tampilkan maksimal 16 karakter

    buzz(1);
    isNFCTapped = true;

    if (modalActive) {
      publishRegister(uuid);
    } else {
      publishAbsensi(uuid);
    }

    isDisplayingResult = true;
    resultDisplayTime = millis();
  }
}

// MQTT Functions with Error Handling
void publishAbsensi(const String& uuid) {
  if (!client.connected()) {
    Serial.println("MQTT not connected, cannot publish absensi");
    lcd.clear();
    lcd.print("MQTT Offline");
    buzz(2);
    return;
  }

  // Validate UUID format
  if (uuid.length() < 8 || uuid.indexOf(":") == -1) {
    Serial.println("Invalid UUID format: " + uuid);
    lcd.clear();
    lcd.print("Invalid Card");
    buzz(2);
    return;
  }

  StaticJsonDocument<250> doc;
  doc["UUIDguru"] = uuid;
  doc["timestamp"] = time(nullptr);
  doc["action"] = "absensi";
  doc["device_id"] = WiFi.macAddress();
  doc["rssi"] = WiFi.RSSI();

  String payload;
  size_t payloadSize = serializeJson(doc, payload);

  if (payloadSize == 0) {
    Serial.println("Failed to serialize JSON for absensi");
    lcd.clear();
    lcd.print("JSON Error");
    buzz(2);
    return;
  }

  Serial.println("Publishing absensi data...");
  Serial.println("Payload: " + payload);

  bool publishSuccess = false;
  int attempts = 0;

  while (!publishSuccess && attempts < 3) {
    attempts++;
    publishSuccess = client.publish(topic_absensi, payload.c_str(), true); // Retain message

    if (!publishSuccess) {
      Serial.println("Publish attempt " + String(attempts) + " failed");
      delay(100);
    }
  }

  if (publishSuccess) {
    Serial.println("Data absensi berhasil dikirim via MQTT");
    lcd.clear();
    lcd.print("Menunggu Server");
    buzz(1);
  } else {
    Serial.println("Gagal kirim data absensi setelah 3 percobaan");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Gagal Kirim");
    lcd.setCursor(0, 1);
    lcd.print("Coba Lagi");
    buzz(3, 150, 100);
  }
}

void publishRegister(const String& uuid) {
  if (!client.connected()) {
    Serial.println("MQTT not connected, cannot publish register");
    lcd.clear();
    lcd.print("MQTT Offline");
    buzz(2);
    return;
  }

  // Validate UUID format
  if (uuid.length() < 8 || uuid.indexOf(":") == -1) {
    Serial.println("Invalid UUID format for registration: " + uuid);
    lcd.clear();
    lcd.print("Invalid Card");
    buzz(2);
    return;
  }

  StaticJsonDocument<250> doc;
  doc["UUIDguru"] = uuid;
  doc["timestamp"] = time(nullptr);
  doc["action"] = "register";
  doc["device_id"] = WiFi.macAddress();
  doc["rssi"] = WiFi.RSSI();

  String payload;
  size_t payloadSize = serializeJson(doc, payload);

  if (payloadSize == 0) {
    Serial.println("Failed to serialize JSON for registration");
    lcd.clear();
    lcd.print("JSON Error");
    buzz(2);
    return;
  }

  Serial.println("Publishing registration data...");
  Serial.println("Payload: " + payload);

  bool publishSuccess = false;
  int attempts = 0;

  while (!publishSuccess && attempts < 3) {
    attempts++;
    publishSuccess = client.publish(topic_register, payload.c_str(), true); // Retain message

    if (!publishSuccess) {
      Serial.println("Register publish attempt " + String(attempts) + " failed");
      delay(100);
    }
  }

  if (publishSuccess) {
    Serial.println("UUID registrasi berhasil dikirim via MQTT");
    lcd.clear();
    lcd.print("Menunggu Server");
    buzz(1);
  } else {
    Serial.println("Gagal kirim UUID registrasi setelah 3 percobaan");
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Gagal Daftar");
    lcd.setCursor(0, 1);
    lcd.print("Coba Lagi");
    buzz(3, 150, 100);
  }
}

void handleMQTTMessage(char* topic, byte* payload, unsigned int length) {
  String message = "";
  for (int i = 0; i < length; i++) {
    message += (char)payload[i];
  }

  Serial.print("MQTT message received [");
  Serial.print(topic);
  Serial.print("]: ");
  Serial.println(message);

  // Parse JSON response
  StaticJsonDocument<300> doc;
  DeserializationError error = deserializeJson(doc, message);

  if (!error) {
    if (String(topic) == topic_response) {
      String action = doc["action"];
      String status = doc["status"];
      String responseUUID = doc["uuid"];

      if (responseUUID == uuid) { // Pastikan response untuk UUID yang sama
        if (action == "absensi") {
          if (status == "success") {
            lcd.clear();
            lcd.print("Absensi Berhasil");
            buzz(1);
          } else if (status == "already_present") {
            lcd.clear();
            lcd.setCursor(0, 0);
            lcd.print("Absensi sudah");
            lcd.setCursor(0, 1);
            lcd.print("lengkap");
            buzz(2);
          } else if (status == "not_registered") {
            lcd.clear();
            lcd.print("Belum Terdaftar");
            buzz(2);
          }
        } else if (action == "register") {
          if (status == "success") {
            lcd.clear();
            lcd.print("Registrasi OK");
            buzz(1);
          }
        }

        isDisplayingResult = true;
        resultDisplayTime = millis();
      }
    } else if (String(topic) == topic_status) {
      bool newModalStatus = doc["modal_active"];
      if (modalActive != newModalStatus) {
        modalActive = newModalStatus;
        Serial.print("Status modal diperbarui: ");
        Serial.println(modalActive ? "ACTIVE" : "INACTIVE");
      }
    }
  }

  // Blink LED untuk indikasi pesan diterima
  digitalWrite(LED_BUILTIN, HIGH);
  delay(100);
  digitalWrite(LED_BUILTIN, LOW);
}

void setup_wifi() {
  delay(10);

  // Try saved WiFi credentials first
  if (strlen(wifiSSID) > 0) {
    Serial.println();
    Serial.print("Connecting to saved WiFi: ");
    Serial.println(wifiSSID);

    WiFi.mode(WIFI_STA);
    WiFi.begin(wifiSSID, wifiPass);

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
      delay(500);
      Serial.print(".");
      attempts++;

      // Update LCD with connection status
      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("WiFi Connecting");
      lcd.setCursor(0, 1);
      for (int i = 0; i < (attempts / 2) % 4; i++) {
        lcd.print(".");
      }
    }

    if (WiFi.status() == WL_CONNECTED) {
      Serial.println("\nWiFi connected to saved network");
      Serial.println("IP address: ");
      Serial.println(WiFi.localIP());

      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("WiFi Connected");
      lcd.setCursor(0, 1);
      lcd.print(WiFi.localIP());
      delay(2000);
      return;
    } else {
      Serial.println("\nFailed to connect to saved WiFi");
    }
  }

  // Fallback to hardcoded credentials
  Serial.println();
  Serial.print("Connecting to fallback WiFi: ");
  Serial.println(ssid);

  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 30) {
    delay(500);
    Serial.print(".");
    attempts++;

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("WiFi Fallback");
    lcd.setCursor(0, 1);
    for (int i = 0; i < (attempts / 2) % 4; i++) {
      lcd.print(".");
    }

    // Restart if too many failed attempts
    if (attempts >= 30) {
      Serial.println("\nWiFi connection failed. Restarting...");
      lcd.clear();
      lcd.print("WiFi Failed");
      lcd.setCursor(0, 1);
      lcd.print("Restarting...");
      delay(3000);
      ESP.restart();
    }
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\nWiFi connected");
    Serial.println("IP address: ");
    Serial.println(WiFi.localIP());

    // Save successful connection
    saveWiFiConfigToPrefs(ssid, password);

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("WiFi Connected");
    lcd.setCursor(0, 1);
    lcd.print(WiFi.localIP());
    delay(2000);
  }
}

void setDateTime() {
  configTime(0, 0, "pool.ntp.org", "time.nist.gov");
  Serial.print("Waiting for NTP time sync: ");
  time_t now = time(nullptr);
  while (now < 8 * 3600 * 2) {
    delay(500);
    Serial.print(".");
    now = time(nullptr);
  }
  Serial.println("");
  struct tm timeinfo;
  gmtime_r(&now, &timeinfo);
  Serial.print("Current time: ");
  Serial.print(asctime(&timeinfo));
}

void callback(char* topic, byte* payload, unsigned int length) {
  handleMQTTMessage(topic, payload, length);
}

void reconnect() {
  static unsigned long lastReconnectAttempt = 0;
  static int reconnectAttempts = 0;

  // Don't attempt reconnection too frequently
  if (millis() - lastReconnectAttempt < 5000) {
    return;
  }
  lastReconnectAttempt = millis();

  // Check WiFi connection first
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi disconnected, attempting to reconnect...");
    lcd.clear();
    lcd.print("WiFi Reconnect");

    WiFi.disconnect();
    WiFi.begin(strlen(wifiSSID) > 0 ? wifiSSID : ssid,
               strlen(wifiSSID) > 0 ? wifiPass : password);

    int wifiAttempts = 0;
    while (WiFi.status() != WL_CONNECTED && wifiAttempts < 10) {
      delay(500);
      wifiAttempts++;
      Serial.print(".");
    }

    if (WiFi.status() != WL_CONNECTED) {
      Serial.println("WiFi reconnection failed");
      lcd.clear();
      lcd.print("WiFi Failed");
      return;
    } else {
      Serial.println("WiFi reconnected");
      lcd.clear();
      lcd.print("WiFi OK");
      delay(1000);
    }
  }

  if (!client.connected()) {
    Serial.print("Attempting MQTT connection... (attempt ");
    Serial.print(reconnectAttempts + 1);
    Serial.print(")");

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("MQTT Connecting");
    lcd.setCursor(0, 1);
    lcd.print("Attempt: ");
    lcd.print(reconnectAttempts + 1);

    String clientId = "ESP32-Absensi-";
    clientId += String(random(0xffff), HEX);

    if (client.connect(clientId.c_str(), mqtt_user, mqtt_pass)) {
      Serial.println(" connected");
      reconnectAttempts = 0; // Reset counter on successful connection

      // Subscribe to response and status topics
      if (client.subscribe(topic_response)) {
        Serial.println("Subscribed to response topic");
      } else {
        Serial.println("Failed to subscribe to response topic");
      }

      if (client.subscribe(topic_status)) {
        Serial.println("Subscribed to status topic");
      } else {
        Serial.println("Failed to subscribe to status topic");
      }

      // Publish device online status
      StaticJsonDocument<150> statusDoc;
      statusDoc["device"] = clientId;
      statusDoc["status"] = "online";
      statusDoc["timestamp"] = time(nullptr);
      statusDoc["ip"] = WiFi.localIP().toString();
      statusDoc["rssi"] = WiFi.RSSI();

      String statusPayload;
      serializeJson(statusDoc, statusPayload);

      if (client.publish(topic_status, statusPayload.c_str())) {
        Serial.println("Device status published");
      } else {
        Serial.println("Failed to publish device status");
      }

      lcd.clear();
      lcd.print("MQTT Connected");
      delay(1000);
      tampilkanSiapAbsen();

    } else {
      reconnectAttempts++;
      Serial.print(" failed, rc=");
      Serial.print(client.state());

      // Handle different error codes
      String errorMsg = "";
      switch (client.state()) {
        case -4: errorMsg = "Timeout"; break;
        case -3: errorMsg = "Lost connection"; break;
        case -2: errorMsg = "Network failed"; break;
        case -1: errorMsg = "Disconnected"; break;
        case 1: errorMsg = "Bad protocol"; break;
        case 2: errorMsg = "Bad client ID"; break;
        case 3: errorMsg = "Unavailable"; break;
        case 4: errorMsg = "Bad credentials"; break;
        case 5: errorMsg = "Unauthorized"; break;
        default: errorMsg = "Unknown"; break;
      }

      Serial.print(" (");
      Serial.print(errorMsg);
      Serial.println(")");

      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("MQTT Error:");
      lcd.setCursor(0, 1);
      lcd.print(errorMsg);

      // Restart if too many failed attempts
      if (reconnectAttempts >= 10) {
        Serial.println("Too many MQTT reconnection attempts. Restarting...");
        lcd.clear();
        lcd.print("MQTT Failed");
        lcd.setCursor(0, 1);
        lcd.print("Restarting...");
        delay(3000);
        ESP.restart();
      }
    }
  }
}

void checkResetButton() {
  static bool lastState = HIGH;
  static unsigned long pressStart = 0;
  static bool resetTriggered = false;

  bool currentState = digitalRead(RESET_BUTTON_PIN);

  if (lastState == HIGH && currentState == LOW) {
    pressStart = millis();
    resetTriggered = false;
  }

  if (currentState == LOW && !resetTriggered) {
    if (millis() - pressStart > 2000) {
      resetTriggered = true;
      lcd.clear();
      lcd.setCursor(0, 0);
      lcd.print("Reset WiFi Config");
      buzz(2, 150, 100);
      resetPreferences();
    }
  }

  if (lastState == LOW && currentState == HIGH) {
    resetTriggered = false;
  }

  lastState = currentState;
}

void checkWakeupPin() {
  // Function untuk monitoring wakeup pin
  // Bisa digunakan untuk deep sleep functionality di masa depan
  static bool lastWakeupState = HIGH;
  bool currentWakeupState = digitalRead(WAKEUP_PIN);

  if (lastWakeupState == HIGH && currentWakeupState == LOW) {
    Serial.println("Wakeup pin activated");
    // Future implementation: wake from deep sleep
    // atau trigger special function
  }

  lastWakeupState = currentWakeupState;
}

void setup() {
  Serial.begin(115200); // Ubah ke 115200 untuk performa lebih baik

  // Initialize pins dengan konfigurasi baru
  pinMode(LED_BUILTIN, OUTPUT);        // D13 → LED
  pinMode(BUZZER_PIN, OUTPUT);         // D27 → Buzzer
  pinMode(RESET_BUTTON_PIN, INPUT_PULLUP); // D26 → Tombol
  pinMode(WAKEUP_PIN, INPUT_PULLUP);   // D2 → Wakeup (optional)
  digitalWrite(LED_BUILTIN, LOW);
  digitalWrite(BUZZER_PIN, LOW);

  // Initialize I2C and SPI
  Wire.begin();
  SPI.begin();

  // Initialize LCD
  lcd.init();
  lcd.backlight();
  lcd.clear();
  lcd.print("Menghubungkan...");

  // Initialize MFRC522
  mfrc522.PCD_Init();
  Serial.println("MFRC522 initialized");

  // Check MFRC522 version
  byte version = mfrc522.PCD_ReadRegister(mfrc522.VersionReg);
  Serial.print("MFRC522 Software Version: 0x");
  Serial.println(version, HEX);

  // Read WiFi config from preferences
  readWiFiConfigFromPrefs();

  // Setup WiFi
  setup_wifi();
  setDateTime();

  // Initialize filesystem
  if (!LittleFS.begin(true)) {
    Serial.println("An Error has occurred while mounting LittleFS");
    return;
  }

  // Load SSL certificate
  File cert = LittleFS.open("/certs.ar");
  if (!cert) {
    Serial.println("Failed to open certs.ar file. Did you upload the filesystem image?");
  } else {
    Serial.println("Successfully opened cert file from LittleFS.");
    espClient.setCACert(cert.readString().c_str());
    cert.close();
  }

  // Setup MQTT
  client.setServer(mqtt_server, 8883);
  client.setCallback(callback);

  // Initial display
  tampilkanSiapAbsen();

  Serial.println("ESP32 MQTT Absensi System Ready!");
}

void loop() {
  // Check MQTT connection
  if (!client.connected()) {
    reconnect();
  }
  client.loop();

  // Check reset button
  checkResetButton();

  // Check for NFC/RFID cards (non-blocking, optimized timing)
  if (millis() - lastNfcCheck > nfcInterval) {
    lastNfcCheck = millis();
    handleNFCCard();
  }

  // Handle result display timeout
  if (isDisplayingResult && millis() - resultDisplayTime >= resultDisplayDuration) {
    tampilkanSiapAbsen();
    isDisplayingResult = false;
  }

  // Update clock display
  if (!isDisplayingResult && millis() - lastClockUpdate > clockUpdateInterval) {
    lastClockUpdate = millis();
    tampilkanSiapAbsen(); // Refresh time display
  }

  // Handle serial commands for testing (optional)
  if (Serial.available() > 0) {
    String command = Serial.readStringUntil('\n');
    command.trim();

    if (command.startsWith("test_uuid:")) {
      String testUUID = command.substring(10);
      Serial.println("Testing with UUID: " + testUUID);
      uuid = testUUID;
      publishAbsensi(testUUID);
    } else if (command == "toggle_modal") {
      modalActive = !modalActive;
      Serial.println("Modal status: " + String(modalActive ? "ACTIVE" : "INACTIVE"));
    } else if (command == "reset_wifi") {
      resetPreferences();
    }
  }
}
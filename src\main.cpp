#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <time.h>
#include <FS.h>
#include <LittleFS.h>

#define LED_BUILTIN 2

// --- GANTI DENGAN INFORMASI ANDA ---
const char* ssid = "Bisadong";
const char* password = "punyakamar14";
const char* mqtt_server = "ffe180c2d6054ac78559eccdc9597e4f.s1.eu.hivemq.cloud";
const char* mqtt_user = "absensi-sekolah";
const char* mqtt_pass = "Acekolah123";

WiFiClientSecure espClient;
PubSubClient client(espClient);

void setup_wifi() {
  delay(10);
  Serial.println();
  Serial.print("Connecting to ");
  Serial.println(ssid);
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nWiFi connected");
  Serial.println("IP address: ");
  Serial.println(WiFi.localIP());
}

void setDateTime() {
  configTime(0, 0, "pool.ntp.org", "time.nist.gov");
  Serial.print("Waiting for NTP time sync: ");
  time_t now = time(nullptr);
  while (now < 8 * 3600 * 2) {
    delay(500);
    Serial.print(".");
    now = time(nullptr);
  }
  Serial.println("");
  struct tm timeinfo;
  gmtime_r(&now, &timeinfo);
  Serial.print("Current time: ");
  Serial.print(asctime(&timeinfo));
}

void callback(char* topic, byte* payload, unsigned int length) {
  Serial.print("Message arrived [");
  Serial.print(topic);
  Serial.print("] ");
  for (int i = 0; i < length; i++) {
    Serial.print((char)payload[i]);
  }
  Serial.println();
  if (length > 0 && payload[0] != '\0') {
    digitalWrite(LED_BUILTIN, HIGH);
    delay(500);
    digitalWrite(LED_BUILTIN, LOW);
  }
}

void reconnect() {
  while (!client.connected()) {
    Serial.print("Attempting MQTT connection...");
    String clientId = "ESP32Client-";
    clientId += String(random(0xffff), HEX);
    if (client.connect(clientId.c_str(), mqtt_user, mqtt_pass)) {
      Serial.println("connected");
      client.publish("testTopic", "hello from ESP32");
      client.subscribe("testTopic");
    } else {
      Serial.print("failed, rc=");
      Serial.print(client.state());
      Serial.println(" try again in 5 seconds");
      delay(5000);
    }
  }
}

void setup() {
  Serial.begin(9600);
  pinMode(LED_BUILTIN, OUTPUT);
  digitalWrite(LED_BUILTIN, LOW);

  setup_wifi();
  setDateTime();

  if (!LittleFS.begin(true)) {
    Serial.println("An Error has occurred while mounting LittleFS");
    return;
  }

  File cert = LittleFS.open("/certs.ar");
  if (!cert) {
    Serial.println("Failed to open certs.ar file. Did you upload the filesystem image?");
  } else {
    Serial.println("Successfully opened cert file from LittleFS.");
    espClient.setCACert(cert.readString().c_str());
    cert.close();
  }

  client.setServer(mqtt_server, 8883);
  client.setCallback(callback);
}

void loop() {
  if (!client.connected()) {
    reconnect();
  }
  client.loop();

  // --- BAGIAN TAMBAHAN UNTUK INPUT SERIAL ---
  // Cek apakah ada data yang dikirim dari Serial Monitor
  if (Serial.available() > 0) {
    // Baca seluruh baris teks sampai user menekan Enter
    String message = Serial.readStringUntil('\n');
    message.trim(); // Hapus spasi di awal/akhir jika ada

    // Kirim pesan ke MQTT jika teks tidak kosong
    if (message.length() > 0) {
      Serial.print("Sending message: ");
      Serial.println(message);
      
      // Publikasikan pesan ke topik "testTopic"
      client.publish("testTopic", message.c_str());
    }
  }
}
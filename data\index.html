<!DOCTYPE html>
<html>
  <head>
    <title>Konfigurasi Absensi</title>
    <script>
      async function loadWiFiList() {
        let response = await fetch("/scanWiFi");
        let ssids = await response.json();
        let select = document.getElementById("ssidSelect");
        select.innerHTML = "";
        let emptyOption = document.createElement("option");
        emptyOption.value = "";
        emptyOption.text = "-- Pilih SSID --";
        emptyOption.disabled = true;
        emptyOption.selected = true;
        select.appendChild(emptyOption);

        ssids.forEach((ssid) => {
          let option = document.createElement("option");
          option.value = ssid;
          option.text = ssid;
          select.appendChild(option);
        });
      }

      function toggleStaticConfig() {
        const mode = document.getElementById("ipModeSelect").value;
        const staticConfigDiv = document.getElementById("staticConfig");
        if (mode === "Static") {
          staticConfigDiv.style.display = "block";
        } else {
          staticConfigDiv.style.display = "none";
          document.getElementById("ipAddress").value = "";
          document.getElementById("gateway").value = "";
          document.getElementById("subnetMask").value = "";
        }
      }
      window.onload = () => {
        loadWiFiList();
        toggleStaticConfig();
        document
          .getElementById("ipModeSelect")
          .addEventListener("change", toggleStaticConfig);
      };
    </script>
    <style>
      .hidden {
        display: none;
      }
      .flex {
        display: flex;
      }
      .justify-center {
        justify-content: center;
      }
      .items-center {
        align-items: center;
      }
      .h-screen {
        height: 100vh;
      }
      .bg-gray-50 {
        background-color: #f9fafb;
      }
      .bg-white {
        background-color: #ffffff;
      }
      .p-8 {
        padding: 2rem;
      }
      .rounded-lg {
        border-radius: 0.5rem;
      }
      .shadow-lg {
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
      }
      .w-96 {
        width: 24rem;
      }
      .text-2xl {
        font-size: 1.5rem;
      }
      .font-semibold {
        font-weight: 600;
      }
      .text-gray-800 {
        color: #1f2937;
      }
      .mb-4 {
        margin-bottom: 1rem;
      }
      .text-sm {
        font-size: 0.875rem;
      }
      .font-medium {
        font-weight: 500;
      }
      .text-gray-700 {
        color: #4b5563;
      }
      .block {
        display: block;
      }
      .w-full {
        width: 100%;
      }
      .px-3 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
      }
      .py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
      }
      .border {
        border-width: 1px;
      }
      .border-gray-300 {
        border-color: #d1d5db;
      }
      .rounded-md {
        border-radius: 0.375rem;
      }
      .focus\:outline-none:focus {
        outline: none;
      }
      .focus\:ring-2:focus {
        ring-width: 2px;
      }
      .focus\:ring-green-500:focus {
        ring-color: #10b981;
      }
      .text-red-500 {
        color: #ef4444;
      }
      .flex {
        display: flex;
      }
      .justify-end {
        justify-content: flex-end;
      }
      .bg-green-600 {
        background-color: #16a34a;
      }
      .text-white {
        color: white;
      }
      .rounded-md {
        border-radius: 0.375rem;
      }
      .hover\:bg-green-700:hover {
        background-color: #15803d;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="flex justify-center items-center h-screen">
      <div class="bg-white p-8 rounded-lg shadow-lg w-96">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">
          Konfigurasi WiFi
        </h2>
        <div id="error" class="text-red-500 mb-4"></div>
        <form method="POST" action="/saveWiFi">
          <div class="mb-4">
            SSID:<br />
            <select id="ssidSelect" name="ssid" required class="rounded-lg">
              <!-- opsi akan dimasukkan via JavaScript --></select
            ><br /><br />
          </div>

          <div class="mb-4">
            <label
              for="password"
              class="block text-sm font-semibold text-gray-700 mb-1"
              >Password</label
            >
            <input
              type="password"
              id="password"
              name="password"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              required
            />
          </div>

          <div class="mb-4">
            <label
              for="ipModeSelect"
              class="block text-sm font-semibold text-gray-700 mb-1"
              >Mode IP</label
            >
            <select
              id="ipModeSelect"
              name="ipMode"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              required
            >
              <option value="Dynamic" selected>Dynamic (DHCP)</option>
              <option value="Static">Static</option>
            </select>
          </div>

          <div id="staticConfig" style="display: none">
            <div class="mb-4">
              <label
                for="ipAddress"
                class="block text-sm font-semibold text-gray-700 mb-1"
                >IP Address</label
              >
              <input
                type="text"
                id="ipAddress"
                name="ipAddress"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="e.g. ***********00"
              />
            </div>

            <div class="mb-4">
              <label
                for="gateway"
                class="block text-sm font-semibold text-gray-700 mb-1"
                >Gateway</label
              >
              <input
                type="text"
                id="gateway"
                name="gateway"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="e.g. ***********"
              />
            </div>

            <div class="mb-4">
              <label
                for="subnetMask"
                class="block text-sm font-semibold text-gray-700 mb-1"
                >Subnet Mask</label
              >
              <input
                type="text"
                id="subnetMask"
                name="subnetMask"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="e.g. 255.255.255.0"
              />
            </div>
          </div>

          <div class="flex rounded-lg">
            <input
              type="submit"
              value="Simpan & Restart"
              class="bg-green-600 rounded-lg"
            />
          </div>
        </form>
      </div>
    </div>
  </body>
</html>

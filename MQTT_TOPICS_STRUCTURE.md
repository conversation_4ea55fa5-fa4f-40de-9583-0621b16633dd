# MQTT Topics Structure untuk ESP32 Absensi System

## Overview
Sistem absensi ESP32 menggunakan MQTT dengan struktur topic yang optimal untuk performa tinggi dan skalabilitas.

## MQTT Topics yang Digunakan

### 1. `absensi/data`
**Publisher**: ESP32 Device  
**Subscriber**: Backend Server  
**Purpose**: Mengirim data absensi ketika kartu RFID di-tap

**Payload Format**:
```json
{
  "UUIDguru": "A1:B2:C3:D4",
  "timestamp": 1672531200,
  "action": "absensi"
}
```

### 2. `absensi/register`
**Publisher**: ESP32 Device  
**Subscriber**: Backend Server  
**Purpose**: Mengirim UUID baru untuk registrasi (ketika modal aktif)

**Payload Format**:
```json
{
  "UUIDguru": "A1:B2:C3:D4", 
  "timestamp": 1672531200,
  "action": "register"
}
```

### 3. `absensi/status`
**Publisher**: Backend Server  
**Subscriber**: ESP32 Device  
**Purpose**: Mengirim status modal dan konfigurasi sistem

**Payload Format**:
```json
{
  "modal_active": true,
  "timestamp": 1672531200
}
```

### 4. `absensi/response`
**Publisher**: Backend Server  
**Subscriber**: ESP32 Device  
**Purpose**: Response dari server untuk konfirmasi absensi/registrasi

**Payload Format**:
```json
{
  "action": "absensi",
  "status": "success|already_present|not_registered",
  "uuid": "A1:B2:C3:D4",
  "message": "Absensi berhasil dicatat",
  "timestamp": 1672531200
}
```

## Keunggulan Struktur MQTT vs HTTP

### 1. **Persistent Connection**
- HTTP: Setiap request membuat koneksi baru
- MQTT: Satu koneksi persistent untuk semua komunikasi

### 2. **Lightweight Protocol**
- HTTP: Header overhead ~200-500 bytes per request
- MQTT: Header overhead ~2-5 bytes per message

### 3. **Event-Driven Architecture**
- HTTP: Polling-based (check status setiap 3-5 detik)
- MQTT: Push-based (instant notification)

### 4. **Network Efficiency**
- HTTP: Multiple TCP handshakes
- MQTT: Single TCP connection dengan keep-alive

## Optimalisasi yang Diterapkan

### 1. **Timing Optimizations**
```cpp
const unsigned long nfcInterval = 300; // 300ms vs 500ms sebelumnya
```

### 2. **Non-blocking Operations**
- Semua operasi MQTT non-blocking
- NFC check dengan interval optimal
- Display update yang efisien

### 3. **Smart Reconnection**
```cpp
void reconnect() {
  // Auto-subscribe ke topics yang diperlukan
  // Publish status online
  // Error handling yang robust
}
```

### 4. **Memory Optimization**
- Menggunakan StaticJsonDocument untuk alokasi memory yang predictable
- String operations yang efisien
- Minimal global variables

## Performance Comparison

| Metric | ESP32 Absensi (HTTP) | ESP32 MQTT |
|--------|---------------------|------------|
| **Response Time** | 2-5 seconds | 200-500ms |
| **Network Overhead** | ~500 bytes/request | ~50 bytes/message |
| **Connection Setup** | Per request | Once |
| **Real-time Updates** | Polling (3-5s delay) | Instant |
| **Battery Efficiency** | Lower | Higher |

## Testing Commands

Untuk testing via Serial Monitor:
```
test_uuid:A1:B2:C3:D4    // Test absensi dengan UUID tertentu
toggle_modal             // Toggle status modal
reset_wifi              // Reset konfigurasi WiFi
```

## Backend Integration Requirements

Backend server harus:
1. Subscribe ke `absensi/data` dan `absensi/register`
2. Publish ke `absensi/response` dan `absensi/status`
3. Implement proper JSON parsing
4. Handle concurrent connections dari multiple ESP32 devices

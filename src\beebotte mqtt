#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>

#define LED_BUILTIN 2

// --- KONFIGURASI JARINGAN ---
const char* ssid = "Bisadong";
const char* password = "punyakamar14";

// --- KONFIGURASI BEEBOTTE (DENGAN PERBAIKAN OTENTIKASI) ---
const char* mqtt_server = "mqtt.beebotte.com";
const int mqtt_port = 8883;
const char* mqtt_topic = "sistem_absensi/data_absen";

// PERBAIKAN: Username adalah gabungan "token:" dengan token asli Anda
const char* mqtt_user = "token:token_0Jdo29miwgcPTT8L"; 
// PERBAIKAN: Password dikosongkan
const char* mqtt_pass = ""; 

WiFiClientSecure espClient;
PubSubClient client(espClient);
unsigned long lastMsg = 0;

void setup_wifi() {
  delay(10);
  Serial.println();
  Serial.print("Connecting to ");
  Serial.println(ssid);
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nWiFi connected");
  Serial.println("IP address: ");
  Serial.println(WiFi.localIP());
}

void callback(char* topic, byte* payload, unsigned int length) {
  Serial.print("Message arrived [");
  Serial.print(topic);
  Serial.print("] ");
  for (int i = 0; i < length; i++) {
    Serial.print((char)payload[i]);
  }
  Serial.println();
}

void reconnect() {
  while (!client.connected()) {
    Serial.print("Attempting MQTT connection to Beebotte...");
    String clientId = "ESP32-Absensi-Client"; // Client ID bisa dibuat statis atau random
    
    // Menggunakan metode otentikasi yang sudah diperbaiki
    if (client.connect(clientId.c_str(), mqtt_user, mqtt_pass)) {
      Serial.println("connected");
      client.publish(mqtt_topic, "{\"data\": {\"device_status\":\"online\"}}");
    } else {
      Serial.print("failed, rc=");
      Serial.print(client.state());
      Serial.println(" try again in 5 seconds");
      delay(5000);
    }
  }
}

void setup() {
  Serial.begin(9600);
  setup_wifi();
  espClient.setInsecure(); // Tetap menggunakan ini untuk koneksi TLS yang mudah
  client.setServer(mqtt_server, mqtt_port);
  client.setCallback(callback);
}

void loop() {
  if (!client.connected()) {
    reconnect();
  }
  client.loop();

  unsigned long now = millis();
  if (now - lastMsg > 15000) {
    lastMsg = now;
    String card_id = "0A:1B:2C:3D";
    char json_payload[100];
    sprintf(json_payload, "{\"data\": {\"card_id\":\"%s\", \"status\":\"masuk\"}}", card_id.c_str());
    Serial.print("Publishing message: ");
    Serial.println(json_payload);
    client.publish(mqtt_topic, json_payload);
  }
}
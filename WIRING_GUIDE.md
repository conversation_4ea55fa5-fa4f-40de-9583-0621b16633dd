# ESP32 MQTT Absensi - Wiring & Testing Guide

## 🔌 Pin Configuration

| Component | ESP32 Pin | GPIO | Function |
|-----------|-----------|------|----------|
| **MFRC522 RFID** | | | |
| SS/CS | D5 | GPIO 5 | SPI Slave Select |
| SCK | D18 | GPIO 18 | SPI Clock |
| MOSI | D23 | GPIO 23 | SPI Master Out |
| MISO | D19 | GPIO 19 | SPI Master In |
| RST | D4 | GPIO 4 | Reset Pin |
| **LCD I2C** | | | |
| SCL | D21 | GPIO 21 | I2C Clock |
| SDA | D22 | GPIO 22 | I2C Data |
| **Fingerprint R503** | | | |
| Wakeup | D2 | GPIO 2 | Trigger Detection |
| TX | GPIO 17 | GPIO 17 | Data Out (Serial2) |
| RX | GPIO 16 | GPIO 16 | Data In (Serial2) |
| **Other** | | | |
| LED | D13 | GPIO 13 | Status LED |
| Buzzer | D27 | GPIO 27 | Audio Feedback |
| Button | D26 | GPIO 26 | Reset Button |

## 🔧 Quick Wiring Guide

### MFRC522 RFID
```
VCC → 3.3V    |  RST → D4     |  MISO → D19
GND → GND     |  SS  → D5     |  MOSI → D23
                               SCK  → D18
```

### LCD I2C 16x2
```
VCC → 5V      |  SDA → D22
GND → GND     |  SCL → D21
```

### Fingerprint R503
```
VCC → 3.3V    |  Wakeup → D2
GND → GND     |  TX → GPIO 17 (Serial2)
              |  RX → GPIO 16 (Serial2)
```

### Other Components
```
LED → D13 (+ 220Ω resistor)
Buzzer → D27
Reset Button → D26 (to GND)
```

## ⚠️ Important Notes
- **MFRC522 & Fingerprint**: 3.3V only (NOT 5V)
- **LCD**: Requires 5V power
- **LED**: Use 220Ω resistor
- **Button**: Long press >2s resets WiFi

## 🧪 Testing Commands

### Serial Monitor Commands
```
# Basic Testing
test_uuid:A1:B2:C3:D4           # Test RFID with specific UUID
test_fingerprint:FP:1234        # Test fingerprint with specific ID
toggle_modal                    # Toggle registration/attendance mode
reset_wifi                      # Reset WiFi configuration

# Dummy Data Testing
send_dummy                      # Send dummy data once
enable_dummy                    # Enable auto dummy data (10s interval)
disable_dummy                   # Disable auto dummy data

# Fingerprint Control
enable_fingerprint              # Enable fingerprint sensor
disable_fingerprint             # Disable fingerprint sensor

# System Info
status                          # Show system status
help                           # Show all available commands
```

### Expected Serial Output
```
=================================
ESP32 MQTT Absensi System
=================================
MFRC522 initialized
MFRC522 Software Version: 0x92
Fingerprint sensor DISABLED for testing
WiFi connected
MQTT connected
=================================
ESP32 MQTT Absensi System Ready!
=================================
Configuration:
- Fingerprint: DISABLED
- Dummy Data: ENABLED (10s interval)
- Modal Mode: ATTENDANCE

Type 'help' for available commands
=================================
```

## 🔍 Troubleshooting

### MFRC522 Issues
- **No detection**: Check 3.3V power, verify SPI pins
- **Version 0x00/0xFF**: Check wiring, ensure RST connected

### LCD Issues
- **Blank display**: Check 5V power, verify I2C address (0x27)
- **Garbled text**: Check SDA/SCL connections

### Fingerprint Issues
- **No response**: Check 3.3V power, verify TX/RX crossed
- **Boot problems**: GPIO 1/3 are boot pins, disconnect during upload

### WiFi/MQTT Issues
- **Connection fails**: Check credentials, signal strength
- **MQTT errors**: Verify broker settings, SSL certificate

## ⚡ Performance
- **RFID Response**: ~300ms
- **Fingerprint Response**: ~1000ms
- **Power Consumption**: 200-470mA peak
- **Memory Usage**: <80% heap

## 📡 MQTT Topics
- `absensi/data` - Attendance data
- `absensi/register` - Registration data
- `absensi/response` - Server responses
- `absensi/status` - System status

### Message Format
```json
{
  "UUIDguru": "A1:B2:C3:D4",
  "method": "rfid|fingerprint",
  "action": "absensi|register",
  "timestamp": 1672531200
}
```
